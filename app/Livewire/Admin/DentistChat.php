<?php

namespace App\Livewire\Admin;

use App\Models\Chat;
use App\Models\Dentist;
use App\Models\Message;

use Livewire\Attributes\Layout;
use Livewire\Component;
use Livewire\WithFileUploads;

#[Layout('components.layouts.app')]
class DentistChat extends Component
{
    use WithFileUploads;

    // Chat state
    public $selectedChatId = null;
    public $selectedDentist = null;
    public $messages = [];

    // Message input
    public $newMessage = '';
    public $uploadedFile = null;

    // Search and filters
    public $searchDentists = '';

    // UI state
    public $showFileUpload = false;

    public function mount()
    {
        $this->loadChats();
    }

    public function loadChats()
    {
        // This will be called to refresh chat list
    }

    public function selectChat($chatId)
    {
        $this->selectedChatId = $chatId;
        $chat = Chat::with('dentist')->find($chatId);

        if ($chat) {
            $this->selectedDentist = $chat->dentist;
            $this->loadMessages();
            $this->markMessagesAsRead();
        }
    }

    public function selectDentist($dentistId)
    {
        $dentist = Dentist::find($dentistId);

        if (!$dentist) {
            return;
        }

        // Find or create chat for this dentist
        $chat = Chat::firstOrCreate(['dentist_id' => $dentistId]);

        $this->selectedChatId = $chat->id;
        $this->selectedDentist = $dentist;
        $this->loadMessages();
        $this->markMessagesAsRead();
    }

    public function loadMessages()
    {
        if (!$this->selectedChatId) {
            $this->messages = [];
            return;
        }

        $this->messages = Message::where('chat_id', $this->selectedChatId)
            ->orderBy('created_at', 'asc')
            ->get()
            ->toArray();
    }

    public function sendMessage()
    {
        if (empty(trim($this->newMessage)) && !$this->uploadedFile) {
            return;
        }

        if (!$this->selectedChatId) {
            return;
        }

        $messageData = [
            'chat_id' => $this->selectedChatId,
            'sender' => 'admin',
            'message' => trim($this->newMessage) ?: null,
            'is_unread' => false, // Admin messages are automatically read
        ];

        // Handle file upload
        if ($this->uploadedFile) {
            $filePath = $this->uploadedFile->store('chat-files', 'public');
            $messageData['file'] = $filePath;
            $messageData['file_type'] = $this->getFileType($this->uploadedFile->getMimeType());
        }

        Message::create($messageData);

        // Reset form
        $this->newMessage = '';
        $this->uploadedFile = null;
        $this->showFileUpload = false;

        // Reload messages
        $this->loadMessages();

        // Dispatch event to scroll to bottom
        $this->dispatch('message-sent');
    }

    public function removeFile()
    {
        $this->uploadedFile = null;
        $this->showFileUpload = false;
    }

    public function toggleFileUpload()
    {
        $this->showFileUpload = !$this->showFileUpload;
        if (!$this->showFileUpload) {
            $this->uploadedFile = null;
        }
    }

    private function getFileType($mimeType)
    {
        if (str_starts_with($mimeType, 'image/')) {
            return 'image';
        } elseif (str_starts_with($mimeType, 'video/')) {
            return 'video';
        } else {
            return 'document';
        }
    }

    private function markMessagesAsRead()
    {
        if (!$this->selectedChatId) {
            return;
        }

        Message::where('chat_id', $this->selectedChatId)
            ->where('sender', 'dentist')
            ->where('is_unread', true)
            ->update(['is_unread' => false]);
    }

    public function render()
    {
        // Get all chats with dentists and latest message
        $chats = Chat::with(['dentist', 'latestMessage'])
            ->whereHas('dentist', function ($query) {
                if ($this->searchDentists) {
                    $query->where('name', 'like', '%' . $this->searchDentists . '%')
                          ->orWhere('email', 'like', '%' . $this->searchDentists . '%');
                }
            })
            ->orderByDesc(function ($query) {
                $query->select('created_at')
                    ->from('messages')
                    ->whereColumn('messages.chat_id', 'chats.id')
                    ->latest()
                    ->limit(1);
            })
            ->get();

        // Get dentists without chats for new chat creation
        $dentistsWithoutChats = Dentist::whereDoesntHave('chat')
            ->when($this->searchDentists, function ($query) {
                $query->where('name', 'like', '%' . $this->searchDentists . '%')
                      ->orWhere('email', 'like', '%' . $this->searchDentists . '%');
            })
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('livewire.admin.dentist-chat', [
            'chats' => $chats,
            'dentistsWithoutChats' => $dentistsWithoutChats,
        ]);
    }
}
