<div class="h-screen flex bg-slate-50 dark:bg-slate-900">
    <!-- Left Sidebar - Chat List -->
    <div class="w-80 bg-white dark:bg-slate-800 border-r border-slate-200 dark:border-slate-700 flex flex-col">
        <!-- Header -->
        <div class="p-4 border-b border-slate-200 dark:border-slate-700">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-slate-900 dark:text-white">Dentist Chats</h2>
                <div class="flex items-center space-x-2">
                    <flux:badge variant="primary" size="sm">
                        {{ $chats->count() + $dentistsWithoutChats->count() }} Total
                    </flux:badge>
                </div>
            </div>

            <!-- Search -->
            <div class="relative">
                <flux:input
                    wire:model.live.debounce.300ms="searchDentists"
                    placeholder="Search dentists..."
                    class="pl-10"
                />
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-4 w-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Chat List -->
        <div class="flex-1 overflow-y-auto">
            <!-- Existing Chats -->
            @forelse($chats as $chat)
                <div
                    wire:click="selectChat({{ $chat->id }})"
                    class="p-4 border-b border-slate-100 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-700 cursor-pointer transition-colors {{ $selectedChatId == $chat->id ? 'bg-indigo-50 dark:bg-indigo-900/20 border-l-4 border-l-indigo-500' : '' }}"
                >
                    <div class="flex items-center space-x-3">
                        <!-- Avatar -->
                        <div class="h-10 w-10 bg-indigo-500 rounded-full flex items-center justify-center text-white font-medium text-sm">
                            {{ $chat->dentist->initials() }}
                        </div>

                        <!-- Chat Info -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <h3 class="text-sm font-medium text-slate-900 dark:text-white truncate">
                                    {{ $chat->dentist->name }}
                                </h3>
                                @if($chat->latestMessage->first())
                                    <span class="text-xs text-slate-500 dark:text-slate-400">
                                        {{ $chat->latestMessage->first()->created_at->format('M j') }}
                                    </span>
                                @endif
                            </div>

                            <div class="flex items-center justify-between mt-1">
                                <p class="text-sm text-slate-600 dark:text-slate-300 truncate">
                                    @if($chat->latestMessage->first())
                                        @if($chat->latestMessage->first()->message)
                                            {{ Str::limit($chat->latestMessage->first()->message, 30) }}
                                        @elseif($chat->latestMessage->first()->file)
                                            <span class="flex items-center">
                                                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                                </svg>
                                                File attachment
                                            </span>
                                        @endif
                                    @else
                                        <span class="text-slate-400 italic">No messages yet</span>
                                    @endif
                                </p>

                                @if($chat->unreadMessagesCount() > 0)
                                    <flux:badge variant="danger" size="sm">
                                        {{ $chat->unreadMessagesCount() }}
                                    </flux:badge>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                @if(!$dentistsWithoutChats->count())
                    <div class="p-8 text-center">
                        <svg class="mx-auto h-12 w-12 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.476L3 21l2.476-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-slate-900 dark:text-white">No chats found</h3>
                        <p class="mt-1 text-sm text-slate-500 dark:text-slate-400">Start a conversation with a dentist.</p>
                    </div>
                @endif
            @endforelse

            <!-- Dentists without chats -->
            @if($dentistsWithoutChats->count() > 0)
                <div class="px-4 py-2 bg-slate-100 dark:bg-slate-700">
                    <h4 class="text-xs font-medium text-slate-500 dark:text-slate-400 uppercase tracking-wide">Start New Chat</h4>
                </div>

                @foreach($dentistsWithoutChats as $dentist)
                    <div
                        wire:click="selectDentist({{ $dentist->id }})"
                        class="p-4 border-b border-slate-100 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-700 cursor-pointer transition-colors"
                    >
                        <div class="flex items-center space-x-3">
                            <!-- Avatar -->
                            <div class="h-10 w-10 bg-slate-400 rounded-full flex items-center justify-center text-white font-medium text-sm">
                                {{ $dentist->initials() }}
                            </div>

                            <!-- Dentist Info -->
                            <div class="flex-1 min-w-0">
                                <h3 class="text-sm font-medium text-slate-900 dark:text-white truncate">
                                    {{ $dentist->name }}
                                </h3>
                                <p class="text-sm text-slate-500 dark:text-slate-400 truncate">
                                    {{ $dentist->email }}
                                </p>
                            </div>

                            <div class="text-xs text-slate-400">
                                New
                            </div>
                        </div>
                    </div>
                @endforeach
            @endif
        </div>
    </div>

    <!-- Right Side - Chat Area -->
    <div class="flex-1 flex flex-col">
        @if($selectedDentist)
            <!-- Chat Header -->
            <div class="bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 p-4">
                <div class="flex items-center space-x-3">
                    <div class="h-10 w-10 bg-indigo-500 rounded-full flex items-center justify-center text-white font-medium">
                        {{ $selectedDentist->initials() }}
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-slate-900 dark:text-white">
                            {{ $selectedDentist->name }}
                        </h3>
                        <p class="text-sm text-slate-500 dark:text-slate-400">
                            {{ $selectedDentist->email }}
                        </p>
                    </div>
                    <div class="ml-auto">
                        <flux:badge variant="{{ $selectedDentist->is_active ? 'success' : 'danger' }}" size="sm">
                            {{ $selectedDentist->is_active ? 'Active' : 'Inactive' }}
                        </flux:badge>
                    </div>
                </div>
            </div>

            <!-- Messages Area -->
            <div class="flex-1 overflow-y-auto p-4 space-y-4" id="messages-container">
                @forelse($messages as $message)
                    <div class="flex {{ $message['sender'] === 'admin' ? 'justify-end' : 'justify-start' }}">
                        <div class="max-w-xs lg:max-w-md">
                            <!-- Message Bubble -->
                            <div class="px-4 py-2 rounded-lg {{ $message['sender'] === 'admin' ? 'bg-indigo-500 text-white' : 'bg-white dark:bg-slate-700 text-slate-900 dark:text-white border border-slate-200 dark:border-slate-600' }}">
                                @if($message['message'])
                                    <p class="text-sm">{{ $message['message'] }}</p>
                                @endif

                                @if($message['file'])
                                    <div class="mt-2">
                                        @if($message['file_type'] === 'image')
                                            <img src="{{ asset('storage/' . $message['file']) }}" alt="Image" class="max-w-full h-auto rounded-lg">
                                        @elseif($message['file_type'] === 'video')
                                            <video controls class="max-w-full h-auto rounded-lg">
                                                <source src="{{ asset('storage/' . $message['file']) }}" type="video/mp4">
                                                Your browser does not support the video tag.
                                            </video>
                                        @else
                                            <div class="flex items-center space-x-2 p-2 bg-slate-100 dark:bg-slate-600 rounded-lg">
                                                <svg class="h-5 w-5 text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-slate-900 dark:text-white truncate">
                                                        {{ basename($message['file']) }}
                                                    </p>
                                                </div>
                                                <a href="{{ asset('storage/' . $message['file']) }}" download class="text-indigo-500 hover:text-indigo-600">
                                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                    </svg>
                                                </a>
                                            </div>
                                        @endif
                                    </div>
                                @endif
                            </div>

                            <!-- Timestamp -->
                            <div class="mt-1 {{ $message['sender'] === 'admin' ? 'text-right' : 'text-left' }}">
                                <span class="text-xs text-slate-500 dark:text-slate-400">
                                    {{ \Carbon\Carbon::parse($message['created_at'])->format('M j, g:i A') }}
                                </span>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="flex items-center justify-center h-full">
                        <div class="text-center">
                            <svg class="mx-auto h-12 w-12 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.476L3 21l2.476-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-slate-900 dark:text-white">No messages yet</h3>
                            <p class="mt-1 text-sm text-slate-500 dark:text-slate-400">Start the conversation by sending a message.</p>
                        </div>
                    </div>
                @endforelse
            </div>

            <!-- Message Input Area -->
            <div class="bg-white dark:bg-slate-800 border-t border-slate-200 dark:border-slate-700 p-4">
                <!-- File Upload Preview -->
                @if($uploadedFile)
                    <div class="mb-4 p-3 bg-slate-50 dark:bg-slate-700 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <svg class="h-5 w-5 text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                </svg>
                                <span class="text-sm text-slate-700 dark:text-slate-300">{{ $uploadedFile->getClientOriginalName() }}</span>
                            </div>
                            <button wire:click="removeFile" class="text-red-500 hover:text-red-600">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                @endif

                <!-- File Upload Area (when toggled) -->
                @if($showFileUpload && !$uploadedFile)
                    <div class="mb-4 p-4 border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg">
                        <div class="text-center">
                            <svg class="mx-auto h-8 w-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            <div class="mt-2">
                                <label for="file-upload" class="cursor-pointer">
                                    <span class="text-sm text-indigo-600 hover:text-indigo-500">Upload a file</span>
                                    <input id="file-upload" wire:model="uploadedFile" type="file" class="sr-only" accept="image/*,video/*,.pdf,.doc,.docx,.txt">
                                </label>
                                <p class="text-xs text-slate-500 mt-1">Images, videos, or documents</p>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Message Input -->
                <form wire:submit="sendMessage" class="flex items-end space-x-2">
                    <div class="flex-1">
                        <flux:textarea
                            wire:model="newMessage"
                            placeholder="Type your message..."
                            rows="1"
                            class="resize-none"
                            wire:keydown.enter.prevent="sendMessage"
                        />
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex items-center space-x-2">
                        <!-- File Upload Toggle -->
                        <flux:button
                            type="button"
                            wire:click="toggleFileUpload"
                            variant="ghost"
                            size="sm"
                            class="p-2 {{ $showFileUpload ? 'text-indigo-600' : 'text-slate-400' }}"
                        >
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                            </svg>
                        </flux:button>

                        <!-- Send Button -->
                        <flux:button
                            type="submit"
                            variant="primary"
                            size="sm"
                            :disabled="empty(trim($newMessage)) && !$uploadedFile"
                        >
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                        </flux:button>
                    </div>
                </form>
            </div>
        @else
            <!-- No Chat Selected -->
            <div class="flex-1 flex items-center justify-center bg-white dark:bg-slate-800">
                <div class="text-center">
                    <svg class="mx-auto h-16 w-16 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.476L3 21l2.476-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"></path>
                    </svg>
                    <h3 class="mt-4 text-lg font-medium text-slate-900 dark:text-white">Select a chat to start messaging</h3>
                    <p class="mt-2 text-sm text-slate-500 dark:text-slate-400">Choose a dentist from the sidebar to begin the conversation.</p>
                </div>
            </div>
        @endif
    </div>
</div>

<!-- Auto-scroll to bottom script -->
<script>
    document.addEventListener('livewire:init', () => {
        Livewire.on('message-sent', () => {
            const container = document.getElementById('messages-container');
            if (container) {
                container.scrollTop = container.scrollHeight;
            }
        });
    });

    // Auto-scroll when messages load
    document.addEventListener('DOMContentLoaded', function() {
        const container = document.getElementById('messages-container');
        if (container) {
            container.scrollTop = container.scrollHeight;
        }
    });
</script>
